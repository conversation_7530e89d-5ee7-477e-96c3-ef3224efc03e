import os
import json
import asyncio
import matplotlib.pyplot as plt
import scienceplots
from mplfonts import use_font
import pandas as pd
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
from pydantic import BaseModel, Field
from typing import Annotated, List, Any, Dict
from mcp.server.fastmcp import FastMCP

# 初始化 MCP 服务器
mcp = FastMCP("PlottingTool")

# --- 设置中文字体 ---
try:
    use_font('Noto Serif CJK SC')
except Exception as e:
    print(f"警告：设置字体失败，可能影响中文显示。请确保 'Noto Serif CJK SC' 已安装。错误信息: {e}")

# --- 图片保存路径设置 ---
BASE_DIR = r"G:\Study\Vue\agent-chat-ui-main"  # 获取当前脚本所在目录
IMAGES_DIR = os.path.join(BASE_DIR, 'public', 'images')
os.makedirs(IMAGES_DIR, exist_ok=True)


# --- 输入数据模型定义 (Pydantic) ---
# 这个模型现在用于验证“转换后”的数据格式
class PlotData(BaseModel):
    日期: List[Any]
    基准水位: List[float]
    三峡水库库水位: List[float]
    三峡水库入库流量: List[float]
    三峡水库出库流量: List[float]


# --- 新增：数据转换函数 ---
def _transform_excel_json_to_columnar(raw_data: List[Dict[str, Any]]) -> Dict[str, List[Any]]:
    """
    将Excel MCP输出的单元格列表JSON转换为绘图所需的列式字典格式。

    :param raw_data: 一个包含多个字典的列表，每个字典代表一个单元格。
    :return: 一个字典，键是列名，值是该列所有数据的列表。
    """
    if not raw_data:
        return {}

    # 1. 提取表头：根据第一行（row: 1）的内容创建列索引到列名的映射
    headers = {item['column']: item['value'] for item in raw_data if item['row'] == 1}

    # 2. 初始化列式数据字典
    columnar_data = {name: [] for name in headers.values()}

    # 3. 提取数据行并按行号排序，确保数据顺序正确
    data_rows = sorted([item for item in raw_data if item['row'] > 1], key=lambda x: x['row'])

    # 4. 填充数据
    for item in data_rows:
        header_name = headers.get(item['column'])
        if header_name and header_name in columnar_data:
            columnar_data[header_name].append(item['value'])

    return columnar_data


# --- 绘图函数定义 ---
def _generate_plot(data_json: str, output_filename: str) -> str:
    """
    内部同步绘图函数，负责数据转换、绘图逻辑和文件保存。
    """
    try:
        # 1. 解析JSON字符串为Python列表
        raw_data_list = json.loads(data_json)

        # 2. ✅ 调用新函数，将原始数据转换为列式格式
        transformed_data_dict = _transform_excel_json_to_columnar(raw_data_list)

        # 3. 验证转换后的数据格式并创建DataFrame
        validated_data = PlotData(**transformed_data_dict)
        df = pd.DataFrame(validated_data.dict())

        # 4. 数据类型转换
        timestamp_col = '日期'
        df[timestamp_col] = pd.to_datetime(df[timestamp_col])

        base_level = df['基准水位']
        water_level = df['三峡水库库水位']
        inner_flow = df['三峡水库入库流量']
        outer_flow = df['三峡水库出库流量']

        # 5. 使用 scienceplots 风格进行绘图
        with plt.style.context(['science', 'ieee', 'no-latex', 'cjk-sc-font']):
            fig, ax1 = plt.subplots(figsize=(8, 4.5))
            ax2 = ax1.twinx()

            # 绘制水位
            line1 = ax1.plot(df[timestamp_col], base_level, label='基准水位', linestyle='-', color='C0')
            line2 = ax1.plot(df[timestamp_col], water_level, label='库水位', color=(255 / 255, 33 / 255, 255 / 255),
                             linestyle='-')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('水位 (m)', color='C0')
            ax1.tick_params(axis='y', labelcolor='C0', labelsize=7)
            ax1.yaxis.set_major_locator(MaxNLocator(7))

            # 绘制流量
            line3 = ax2.plot(df[timestamp_col], inner_flow, label='入库流量', linestyle='--',
                             color=(252 / 255, 144 / 255, 107 / 255))
            line4 = ax2.plot(df[timestamp_col], outer_flow, label='出库流量', linestyle='--',
                             color=(88 / 255, 97 / 255, 172 / 255))
            ax2.set_ylabel(r'流量 ($\mathrm{m^3/s}$)')
            ax2.tick_params(axis='y', labelsize=7)
            ax2.yaxis.set_major_locator(MaxNLocator(7))

            # X轴格式化
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m/%d'))
            ax1.tick_params(axis='x', labelsize=7)
            ax1.xaxis.set_major_locator(MaxNLocator(7))
            fig.autofmt_xdate()

            # 合并图例 (使用推荐的方案一，放置在图表上方)
            lines = line1 + line2 + line3 + line4
            labels = [l.get_label() for l in lines]
            ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=len(labels), fontsize=7)

            # 其他设置
            ax1.minorticks_off()
            ax2.minorticks_off()
            fig.tight_layout()

            # 6. 保存图形
            abs_path = os.path.join(IMAGES_DIR, output_filename)
            fig.savefig(abs_path, dpi=300)

            # 7. 返回图片的相对路径
            rel_path = os.path.join('images', output_filename)
            return json.dumps(
                {"status": "success", "message": f"✅ 图片已保存，路径为: {rel_path}", "image_path": rel_path},
                ensure_ascii=False)

    except json.JSONDecodeError:
        return json.dumps({"status": "error", "message": "JSON数据解析失败，请检查格式是否正确。"})
    except Exception as e:
        return json.dumps({"status": "error", "message": f"绘图失败：{e}"})
    finally:
        plt.close('all')


description = """
该工具用于根据从Excel MCP工具读取的原始JSON数据生成包含水位和流量的双Y轴折线图。
它能自动处理单元格列表格式的数据，将其转换为图表所需格式。
生成的图片将被保存在服务器，并返回图片的相对路径。
"""


@mcp.tool()
async def generate_water_level_flow_plot(
        data_json: Annotated[str, Field(description=description)],
        filename: Annotated[str, Field(description="生成的图片文件名，例如 'water_level_flow.png'")]
) -> str:
    """
    根据给定的从Excel MCP输出的原始JSON字符串数据，生成一张双Y轴水位和流量图。

    :param data_json: 包含绘图数据的JSON字符串。JSON格式必须是单元格对象的列表，例如：
                      '[{"address": "A1", "value": "日期", ...}, {"address": "B1", ...}, ...]'
    :param filename: 指定生成的图片文件名，必须包含文件扩展名（如.png）。
    :return: 包含图片保存状态和相对路径的JSON字符串。
    """
    result = await asyncio.to_thread(_generate_plot, data_json, filename)
    print("绘图工具执行完毕。")
    return result


if __name__ == "__main__":
    print("绘图 MCP 服务已启动...")
    mcp.run(transport='stdio')