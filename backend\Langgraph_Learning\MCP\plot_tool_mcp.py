import os
import json
import asyncio
import matplotlib.pyplot as plt
import scienceplots
from mplfonts import use_font
import pandas as pd
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
from pydantic import BaseModel, Field
from typing import Annotated, List, Any
from mcp.server.fastmcp import FastMCP

# 初始化 MCP 服务器
mcp = FastMCP("PlottingTool")

# --- 设置中文字体 ---
# 确保系统已安装 'Noto Serif CJK SC' 字体，否则请更换为其他可用字体
# 注意：在部署环境中需要确保字体可用
try:
    use_font('Noto Serif CJK SC')
except Exception as e:
    print(f"警告：设置字体失败，可能影响中文显示。请确保 'Noto Serif CJK SC' 已安装。错误信息: {e}")

# --- 图片保存路径设置 ---
# 假设图片将保存在项目根目录下的 'public/images' 文件夹中
# 请根据你的实际项目结构调整此路径
# BASE_DIR = os.path.dirname(os.path.abspath(__file__))  # 获取当前脚本所在目录
BASE_DIR = r"G:\Study\Vue\agent-chat-ui-main"  # 获取当前脚本所在目录
IMAGES_DIR = os.path.join(BASE_DIR, 'public', 'images')
os.makedirs(IMAGES_DIR, exist_ok=True)


# --- 输入数据模型定义 (可选但推荐) ---
# 定义一个Pydantic模型来描述期望的JSON数据格式，增加工具的健壮性
class PlotData(BaseModel):
    """用于绘图的数据结构"""
    日期: List[Any]
    基准水位: List[float]
    三峡水库库水位: List[float]
    三峡水库入库流量: List[float]
    三峡水库出库流量: List[float]


# --- 绘图函数定义 ---
def _generate_plot(data_json: str, output_filename: str) -> str:
    """
    内部同步绘图函数，负责核心的绘图逻辑和文件保存。
    """
    try:
        # 1. 解析JSON字符串为Python字典
        data_dict = json.loads(data_json)

        # 2. 验证数据格式并创建DataFrame
        # 这里使用Pydantic模型进行验证，如果数据格式不符会抛出ValidationError
        validated_data = PlotData(**data_dict)
        df = pd.DataFrame(validated_data.dict())

        # 3. 数据类型转换
        timestamp_col = '日期'
        df[timestamp_col] = pd.to_datetime(df[timestamp_col])

        base_level = df['基准水位']
        water_level = df['三峡水库库水位']
        inner_flow = df['三峡水库入库流量']
        outer_flow = df['三峡水库出库流量']

        # 4. 使用 scienceplots 风格进行绘图
        with plt.style.context(['science', 'ieee', 'no-latex', 'cjk-sc-font']):
            # fig, ax1 = plt.subplots(figsize=(8, 4.5))
            fig, ax1 = plt.subplots()
            ax2 = ax1.twinx()

            # 绘制水位
            line1 = ax1.plot(df[timestamp_col], base_level, label='基准水位', linestyle='-', color='C0')
            line2 = ax1.plot(df[timestamp_col], water_level, label='库水位', color=(255 / 255, 33 / 255, 255 / 255),
                             linestyle='-')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('水位 (m)', color='C0')
            ax1.tick_params(axis='y', labelcolor='C0', labelsize=7)
            ax1.yaxis.set_major_locator(MaxNLocator(7))

            # 绘制流量
            line3 = ax2.plot(df[timestamp_col], inner_flow, label='入库流量', linestyle='--',
                             color=(252 / 255, 144 / 255, 107 / 255))
            line4 = ax2.plot(df[timestamp_col], outer_flow, label='出库流量', linestyle='--',
                             color=(88 / 255, 97 / 255, 172 / 255))
            ax2.set_ylabel(r'流量 ($\mathrm{m^3/s}$)')
            ax2.tick_params(axis='y', labelsize=7)
            ax2.yaxis.set_major_locator(MaxNLocator(7))

            # X轴格式化
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m/%d'))
            ax1.tick_params(axis='x', labelsize=7)
            ax1.xaxis.set_major_locator(MaxNLocator(7))
            fig.autofmt_xdate()

            # 合并图例
            lines = line1 + line2 + line3 + line4
            labels = [l.get_label() for l in lines]
            # ax1.legend(lines, labels, loc='upper left', fontsize=7)
            # 将图例放置在图表上方居中位置，并水平排列
            ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=4, fontsize=7)
            # 自动选择最佳位置
            # ax1.legend(lines, labels, loc='best', fontsize=7)

            # 其他设置
            ax1.minorticks_off()
            ax2.minorticks_off()
            fig.tight_layout()

            # 5. 保存图形
            abs_path = os.path.join(IMAGES_DIR, output_filename)
            fig.savefig(abs_path, dpi=300)

            # 6. 返回图片的相对路径，供前端使用
            rel_path = os.path.join('images', output_filename)
            return json.dumps(
                {"status": "success", "message": f"✅ 图片已保存，路径为: {rel_path}", "image_path": rel_path},
                ensure_ascii=False)

    except json.JSONDecodeError:
        return json.dumps({"status": "error", "message": "JSON数据解析失败，请检查格式是否正确。"})
    except Exception as e:
        return json.dumps({"status": "error", "message": f"绘图失败：{e}"})
    finally:
        # 确保关闭所有matplotlib的图表，防止内存泄漏
        plt.close('all')


description = """
该工具用于根据提供的JSON数据生成一个包含水位和流量的双Y轴折线图。
它不读取本地文件，所有数据都以JSON字符串形式传入。
生成的图片将被保存在服务器的特定目录，并返回图片的相对路径。
"""


@mcp.tool()
async def generate_water_level_flow_plot(
        data_json: Annotated[str, Field(description=description)],
        filename: Annotated[str, Field(description="生成的图片文件名，例如 'water_level_flow.png'")]
) -> str:
    """
    根据给定的JSON字符串数据，生成一张双Y轴水位和流量图。

    此工具用于处理水利数据可视化任务。
    :param data_json: 包含绘图数据的JSON字符串。JSON格式必须符合以下结构：
                      {"日期": ["2023-01-01", ...], "基准水位": [172.0, ...],
                       "三峡水库库水位": [170.0, ...], "三峡水库入库流量": [30000.0, ...],
                       "三峡水库出库流量": [28000.0, ...]}
                      所有值都必须是列表。
    :param filename: 指定生成的图片文件名，必须包含文件扩展名（如.png）。
    :return: 包含图片保存状态和相对路径的JSON字符串。
    """
    # 使用 asyncio.to_thread 在单独的线程中运行阻塞的绘图操作
    result = await asyncio.to_thread(_generate_plot, data_json, filename)
    print("绘图工具执行完毕。")
    return result


if __name__ == "__main__":
    print("绘图 MCP 服务已启动...")
    mcp.run(transport='stdio')