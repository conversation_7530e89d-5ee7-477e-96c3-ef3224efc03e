import os
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

# 创建智谱AI LLM实例
llm = ChatOpenAI(
    temperature=0.6,
    model="glm-4.5",
    openai_api_key="3d0a2023739b4d448b7ea766cf24ec0a.6oFLnaf7BnpueQRf",
    openai_api_base="https://open.bigmodel.cn/api/paas/v4/"
)

# 创建消息
messages = [
    SystemMessage(content="你是一个有用的AI助手"),
    HumanMessage(content="请介绍一下人工智能的发展历程")
]

# 调用模型
response = llm(messages)
print(response.content)