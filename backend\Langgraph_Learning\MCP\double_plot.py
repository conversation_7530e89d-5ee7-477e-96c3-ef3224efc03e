import matplotlib.pyplot as plt
import scienceplots
from mplfonts import use_font
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import FixedLocator, MultipleLocator, AutoMinorLocator, MaxNLocator
import matplotlib.lines as mlines

# 设置中文字体，确保图表中的中文能正确显示
use_font('Noto Serif CJK SC')

# --- 数据读取和预处理 ---
# 假设Excel文件与脚本在同一目录下，如果不是，请提供完整路径
# 注意：由于我无法访问你的本地文件 'G:\Work\...'，我将假设数据已成功加载
# 在你本地运行时，请确保文件路径 'file_path' 是正确的
try:
    file_path = r'G:\Work\调洪方案agent展示\agent\output\test.xlsx'
    df = pd.read_excel(file_path)
except FileNotFoundError:
    # 如果找不到文件，创建一个示例DataFrame以便代码可以运行
    print("警告：未找到指定的Excel文件。将使用示例数据生成图表。")
    dates = pd.to_datetime(pd.date_range(start='2023-01-01', periods=100))
    df = pd.DataFrame({
        '日期': dates,
        '基准水位': 172 + 5 * np.sin(np.linspace(0, 10, 100)),
        '三峡水库库水位': 170 + 5 * np.sin(np.linspace(0, 10, 100)),
        '三峡水库入库流量': 30000 + 15000 * np.sin(np.linspace(0, 10, 100)),
        '三峡水库出库流量': 28000 + 12000 * np.sin(np.linspace(0, 10, 100) + 0.5)
    })


# --- 数据提取和转换 ---
timestamp_col = '日期'
df[timestamp_col] = pd.to_datetime(df[timestamp_col])

base_level = df['基准水位']
water_level = df['三峡水库库水位']
inner_flow = df['三峡水库入库流量']
outer_flow = df['三峡水库出库流量']

# --- 使用 scienceplots 风格进行绘图 ---
with plt.style.context(['science', 'ieee', 'no-latex', 'cjk-sc-font']):
    # 创建一个图形和一个坐标轴(ax1)
    # fig, ax1 = plt.subplots(figsize=(8, 4.5)) # 调整图形大小以获得更好的显示效果
    fig, ax1 = plt.subplots()

    # --- 创建共享X轴的第二个Y轴 (ax2) ---
    ax2 = ax1.twinx()

    # --- 在左侧Y轴(ax1)上绘制水位 ---
    # 使用 color='C0' (蓝色) 来表示第一组数据
    line1 = ax1.plot(df[timestamp_col], base_level, label='基准水位', linestyle='-')
    line2 = ax1.plot(df[timestamp_col], water_level, label='库水位', color=(255/255, 33/255, 255/255), linestyle='-')
    ax1.set_xlabel('时间')
    ax1.set_ylabel('水位 (m)', color='C0')
    ax1.tick_params(axis='y', labelcolor='C0', labelsize=7)
    # 设置Y轴刻度数量
    ax1.yaxis.set_major_locator(MaxNLocator(7))
    # --- 在右侧Y轴(ax2)上绘制流量 ---
    # 使用 color='C1' (橙色) 和 'C2' (绿色)
    line3 = ax2.plot(df[timestamp_col], inner_flow, label='入库流量', linestyle='--', color=(252 / 255, 144 / 255, 107 / 255))
    line4 = ax2.plot(df[timestamp_col], outer_flow, label='出库流量', linestyle='--', color=(88 / 255, 97 / 255, 172 / 255))
    # ax2.set_ylabel(r'流量 ($\mathrm{m^3/s}$)', color='C1')
    ax2.set_ylabel(r'流量 ($\mathrm{m^3/s}$)')
    ax2.tick_params(axis='y', labelsize=7)
    # ax2.tick_params(axis='y', labelcolor='C1', labelsize=7)
    # 设置Y轴刻度数量
    ax2.yaxis.set_major_locator(MaxNLocator(7))

    # --- X轴格式化 ---
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m/%d'))
    ax1.tick_params(axis='x', labelsize=7)
    # 设置X轴刻度数量
    ax1.xaxis.set_major_locator(MaxNLocator(7))
    # 自动旋转日期标签以防重叠
    fig.autofmt_xdate()


    # --- 合并图例 ---
    # 将两个坐标轴的线和标签合并到一个图例中
    lines = line1 + line2 + line3 + line4
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left', fontsize=7)


    # --- 其他设置 ---
    # 明确关闭副刻度
    ax1.minorticks_off()
    ax2.minorticks_off()

    # 调整布局以防止标签被裁剪
    fig.tight_layout()

    # 保存图形
    # fig.savefig('dual_axis_plot.png', dpi=300)

    # 显示图形
    plt.show()
