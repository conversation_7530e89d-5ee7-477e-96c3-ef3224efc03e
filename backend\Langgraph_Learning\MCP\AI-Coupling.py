import matplotlib.pyplot as plt
import scienceplots
from mplfonts import use_font
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import FixedLocator, MultipleLocator, AutoMinorLocator, MaxNLocator
import matplotlib.lines as mlines

use_font('Noto Serif CJK SC')

stations = ['宜昌', '枝城', '沙市', '监利', '螺山', '城陵矶']
station_name = stations[4]
# tag = 'level'
tag = 'flow'
Max = 7

# need_low_correction = False
# need_correction = False

start = 0
end = 1440
if station_name == '城陵矶':
    start = 0
    end = 1431
# # 定义所需的刻度日期
# desired_ticks = [
#     datetime(2023, 1, 1),
#     datetime(2023, 5, 1),
#     datetime(2023, 9, 1),
#     datetime(2024, 1, 1),
# ]


file_path = r'G:\Work\调洪方案agent展示\agent\output\test.xlsx'

# fig_path = 'D://Study//大论文//pyModel//pre_model//utils//fig//'

df = pd.read_excel(file_path)

# timestamp_col1 = '时间1'
timestamp_col2 = '日期'
# observed = '实测'
# Tradition = '传统耦合'
# AI = 'AI耦合'

# 将predicted数据的第1、7、13、19、25行数据，也就是每隔6个数据取一个，作为一个新的时间序列
# newPredicted = df[predicted].iloc[::6][start:end]


water_level = df['三峡水库库水位']
inner = df['三峡水库入库流量']
outer = df['三峡水库出库流量']

water_level = np.array(water_level)
inner = np.array(inner)
outer = np.array(outer)

df[timestamp_col2] = pd.to_datetime(df[timestamp_col2])

start_time = df[timestamp_col2].min()
end_time = df[timestamp_col2].max()

with plt.style.context(['science', 'ieee', 'no-latex', 'cjk-sc-font']):
    fig, ax = plt.subplots()
    ax.plot(df[timestamp_col2], water_level, label='实测', color='slategrey', linestyle='-',
            marker='^', markerfacecolor='none', markersize=2.7)#3.5
    ax.plot(df[timestamp_col2], inner, label='一、二维耦合模型', linestyle='-',
            color=(88 / 255, 97 / 255, 172 / 255)) #color=(88 / 255, 97 / 255, 172 / 255)
    ax.plot(df[timestamp_col2], outer, label='STKS-Attention耦合模型', linestyle='-',
            color=(232 / 255, 68 / 255, 69 / 255))
    #(242 / 255, 128 / 255, 128 / 255)
    # ax.plot(df[timestamp_col2], step11, label='step11', linestyle='-',
    #         color=(35 / 255, 186 / 255, 197 / 255))
    # ax.plot(df[timestamp_col2], LSTMLOW, label='LSTMLOW', linestyle='-',
    #         color=(253 / 255, 118 / 255, 63 / 255))
    # ax.plot(df[timestamp_col2][start:end], taoyuanData, label='桃源站流量', linestyle='-',
    #         color=(181 / 255, 84 / 255, 137 / 255))

    # 设置主刻度而去除副刻度
    ax.xaxis.set_major_locator(MaxNLocator(4))
    ax.yaxis.set_major_locator(MaxNLocator(Max))

    # 明确关闭副刻度
    ax.minorticks_off()

    # 设置 X 轴刻度为指定的日期
    # ax.set_xticks(desired_ticks)
    # 格式化 X 轴的日期标签
    # ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
    # 设置x轴字体大小
    ax.tick_params(axis='x', labelsize=7)
    # 设置y轴字体大小
    ax.tick_params(axis='y', labelsize=7)
    # 设置图例字体大小
    # ax.legend(fontsize=5.7, markerfirst=True, markerscale=1.5)  # 设置图例中标记的大小
    # ax.legend()
    ax.set(xlabel='时间')
    if tag == 'level':
        ax.set(ylabel='水位 (m)')
    else:
        ax.set(ylabel=r'流量 ($\mathrm{m^3/s}$)')
    # ax.set(ylabel='流量 (m3/s)')

    # 设置图表标题
    # ax.set_title('Default Plot Style Of Matplotlib')
    # 表中添加文字
    # ax.text(.3, .93, 'NSE=0.9', transform=ax.transAxes, ha='center', va='center')
    # 设置y轴最大值
    # plt.ylim(ymax=50)
    # ax.set_ylim(25, 50)
    # fig.autofmt_xdate()
    # ax.set_xlim(start_time, end_time)
    # ax.xaxis.set_major_locator(mdates.YearLocator())

    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m/%d'))
    # ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
    # ax.xaxis.set_minor_locator(mdates.MonthLocator(interval=1))  # 月副刻度


    # if is_calibration:
    #     ax.xaxis.set_minor_locator(mdates.MonthLocator(interval=3))  # 月副刻度
    #     # 周副刻度
    #     # ax.xaxis.set_minor_locator(mdates.WeekdayLocator(byweekday=mdates.MO))
    # else:
    #     ax.xaxis.set_minor_locator(mdates.MonthLocator(interval=1))  # 月副刻度


    # fig.savefig('D:\\Study\\大论文\\data\\AI耦合模型计算结果\\AI-Coupling-fig\\' + station_name + '-' + tag + '.png', dpi=300)
    plt.show()