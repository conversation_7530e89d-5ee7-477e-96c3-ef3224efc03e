"""
Open Canvas 工具模式定义
定义各种工具的输入输出模式
"""
from typing import Literal, Optional
from pydantic import BaseModel, Field
from .state import ProgrammingLanguageOptions, ArtifactType


class GenerateArtifactSchema(BaseModel):
    """生成工件的工具模式"""
    type: ArtifactType = Field(
        description="工件类型：'code' 表示代码，'text' 表示文本/markdown"
    )
    language: ProgrammingLanguageOptions = Field(
        description="如果是代码工件，指定编程语言"
    )
    title: str = Field(
        description="工件的标题，简洁描述内容"
    )
    artifact: str = Field(
        description="工件的实际内容。对于代码，不要包含三重反引号。对于文本，使用markdown格式。"
    )


class RewriteArtifactSchema(BaseModel):
    """重写工件的工具模式"""
    type: ArtifactType = Field(
        description="工件类型：'code' 表示代码，'text' 表示文本/markdown"
    )
    language: ProgrammingLanguageOptions = Field(
        description="如果是代码工件，指定编程语言"
    )
    title: str = Field(
        description="工件的标题，简洁描述内容"
    )
    artifact: str = Field(
        description="重写后的工件内容。对于代码，不要包含三重反引号。对于文本，使用markdown格式。"
    )


class UpdateArtifactSchema(BaseModel):
    """更新工件的工具模式"""
    artifact: str = Field(
        description="更新后的完整工件内容。保持原有格式和结构。"
    )


class UpdateHighlightedTextSchema(BaseModel):
    """更新高亮文本的工具模式"""
    updatedText: str = Field(
        description="更新后的高亮文本内容，只包含被高亮的部分"
    )


class RewriteArtifactThemeSchema(BaseModel):
    """重写工件主题的工具模式"""
    artifact: str = Field(
        description="根据新主题重写的工件内容"
    )


class RewriteCodeArtifactThemeSchema(BaseModel):
    """重写代码工件主题的工具模式"""
    artifact: str = Field(
        description="根据新要求重写的代码内容"
    )
    language: Optional[ProgrammingLanguageOptions] = Field(
        default=None,
        description="如果语言发生变化，指定新的编程语言"
    )


class CustomActionSchema(BaseModel):
    """自定义操作的工具模式"""
    artifact: str = Field(
        description="执行自定义操作后的工件内容"
    )


class GenerateFollowupSchema(BaseModel):
    """生成后续建议的工具模式"""
    followup: str = Field(
        description="基于当前工件的后续建议或改进方案"
    )


class GenerateTitleSchema(BaseModel):
    """生成对话标题的工具模式"""
    title: str = Field(
        description="简洁的对话标题，不超过50个字符"
    )


class ReflectionSchema(BaseModel):
    """反思记忆的工具模式"""
    insights: str = Field(
        description="从对话中提取的用户偏好、风格指南或重要信息"
    )
    style_guidelines: str = Field(
        description="用户的写作或编码风格偏好"
    )


class WebSearchQuerySchema(BaseModel):
    """网络搜索查询模式"""
    query: str = Field(
        description="搜索查询字符串"
    )
    max_results: int = Field(
        default=5,
        description="最大搜索结果数量"
    )


class SummarizerSchema(BaseModel):
    """摘要工具模式"""
    summary: str = Field(
        description="对话历史的简洁摘要"
    )


# 工具描述常量
ARTIFACT_TOOL_DESCRIPTION = """
生成一个新的工件（代码或文本内容）。工件是用户可以查看、编辑和迭代的内容。
使用此工具创建：
- 代码文件、脚本、函数
- 文档、文章、邮件
- 创意写作内容
- 任何用户请求的结构化内容
"""

REWRITE_ARTIFACT_DESCRIPTION = """
完全重写现有工件。当用户要求重大更改或完全不同的方法时使用。
这会替换整个工件内容。
"""

UPDATE_ARTIFACT_DESCRIPTION = """
更新现有工件的特定部分。用于增量修改、添加功能或修复问题。
保持工件的整体结构和风格。
"""

UPDATE_HIGHLIGHTED_TEXT_DESCRIPTION = """
更新用户在工件中高亮选择的特定文本。
只修改高亮的部分，保持其他内容不变。
"""

REWRITE_ARTIFACT_THEME_DESCRIPTION = """
根据新的主题、风格或要求重写工件。
例如：改变语言、调整长度、修改阅读级别等。
"""

REWRITE_CODE_ARTIFACT_THEME_DESCRIPTION = """
根据新的代码要求重写代码工件。
例如：添加注释、添加日志、移植到其他语言、修复bug等。
"""

CUSTOM_ACTION_DESCRIPTION = """
执行用户定义的自定义操作。
根据用户的具体指令修改工件。
"""

GENERATE_FOLLOWUP_DESCRIPTION = """
基于当前工件生成后续建议。
提供改进建议、扩展想法或相关功能建议。
"""

GENERATE_TITLE_DESCRIPTION = """
为当前对话生成一个简洁的标题。
标题应该反映对话的主要内容或目标。
"""

REFLECTION_DESCRIPTION = """
从对话中提取用户偏好和风格指南。
这些信息将用于改善未来的交互。
"""

WEB_SEARCH_DESCRIPTION = """
在网络上搜索相关信息以增强回答。
当需要最新信息或外部资源时使用。
"""

SUMMARIZER_DESCRIPTION = """
总结对话历史以管理上下文长度。
保留重要信息的同时减少token使用。
"""
