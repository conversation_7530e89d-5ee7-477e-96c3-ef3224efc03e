"""
Open Canvas State Management
定义LangGraph状态结构，对应原始TypeScript版本的状态管理
"""
from typing import List, Optional, Dict, Any, Literal, Union, Sequence
from typing_extensions import TypedDict, Annotated
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel
import operator


# 语言选项
LanguageOptions = Literal["english", "mandarin", "spanish", "french", "hindi"]

# 编程语言选项
ProgrammingLanguageOptions = Literal[
    "typescript", "javascript", "cpp", "java", "php", "python", 
    "html", "sql", "json", "rust", "xml", "clojure", "csharp", "other"
]

# 文章长度选项
ArtifactLengthOptions = Literal["shortest", "short", "long", "longest"]

# 阅读级别选项
ReadingLevelOptions = Literal["pirate", "child", "teenager", "college", "phd"]

# 工件类型
ArtifactType = Literal["code", "text"]


class CodeHighlight(BaseModel):
    """代码高亮信息"""
    text: str
    startLine: int
    endLine: int
    startColumn: int
    endColumn: int


class TextHighlight(BaseModel):
    """文本高亮信息"""
    text: str
    markdownBlocks: List[str]


class SearchResult(BaseModel):
    """搜索结果"""
    title: str
    url: str
    content: str


class ArtifactContentV3(BaseModel):
    """工件内容基类"""
    index: int
    type: ArtifactType
    title: str


class ArtifactCodeV3(ArtifactContentV3):
    """代码工件"""
    type: Literal["code"] = "code"
    code: str
    language: ProgrammingLanguageOptions


class ArtifactMarkdownV3(ArtifactContentV3):
    """文本工件"""
    type: Literal["text"] = "text"
    fullMarkdown: str


class ArtifactV3(BaseModel):
    """工件容器"""
    currentIndex: int
    contents: List[Union[ArtifactCodeV3, ArtifactMarkdownV3]]


def update_messages(existing: List[BaseMessage], updates: Union[BaseMessage, List[BaseMessage]]) -> List[BaseMessage]:
    """消息更新函数"""
    return add_messages(existing, updates)


def update_artifact(existing: Optional[ArtifactV3], updates: Optional[ArtifactV3]) -> Optional[ArtifactV3]:
    """工件更新函数"""
    if updates is None:
        return existing
    return updates


class OpenCanvasState(TypedDict):
    """
    Open Canvas 图状态定义
    对应原始TypeScript版本的OpenCanvasGraphAnnotation
    """
    # 消息列表 - 显示给用户的完整对话历史
    messages: Annotated[List[BaseMessage], add_messages]
    
    # 内部消息列表 - 传递给模型的消息，可能包含摘要等
    _messages: Annotated[List[BaseMessage], add_messages]
    
    # 用户高亮的代码部分
    highlightedCode: Optional[CodeHighlight]
    
    # 用户高亮的文本部分
    highlightedText: Optional[TextHighlight]
    
    # 当前工件
    artifact: Optional[ArtifactV3]
    
    # 下一个要路由到的节点
    next: Optional[str]
    
    # 翻译目标语言
    language: Optional[LanguageOptions]
    
    # 工件长度设置
    artifactLength: Optional[ArtifactLengthOptions]
    
    # 是否使用表情符号重新生成
    regenerateWithEmojis: Optional[bool]
    
    # 阅读级别
    readingLevel: Optional[ReadingLevelOptions]
    
    # 是否添加代码注释
    addComments: Optional[bool]
    
    # 是否添加日志
    addLogs: Optional[bool]
    
    # 代码移植目标语言
    portLanguage: Optional[ProgrammingLanguageOptions]
    
    # 是否修复bug
    fixBugs: Optional[bool]
    
    # 自定义快速操作ID
    customQuickActionId: Optional[str]
    
    # 是否启用网络搜索
    webSearchEnabled: Optional[bool]
    
    # 网络搜索结果
    webSearchResults: Optional[List[SearchResult]]


# 默认输入状态
DEFAULT_INPUTS = {
    "messages": [],
    "_messages": [],
    "highlightedCode": None,
    "highlightedText": None,
    "artifact": None,
    "next": None,
    "language": None,
    "artifactLength": None,
    "regenerateWithEmojis": None,
    "readingLevel": None,
    "addComments": None,
    "addLogs": None,
    "portLanguage": None,
    "fixBugs": None,
    "customQuickActionId": None,
    "webSearchEnabled": None,
    "webSearchResults": None,
}


class GraphConfig(TypedDict):
    """图配置"""
    customModelName: str
    temperature: float
    maxTokens: int
    userId: Optional[str]
    threadId: Optional[str]
