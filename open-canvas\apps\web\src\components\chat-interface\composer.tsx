"use client";

import { ComposerPrimitive, ThreadPrimitive } from "@assistant-ui/react";
import { type FC, useState, useEffect } from "react";

import { TooltipIconButton } from "@/components/ui/assistant-ui/tooltip-icon-button";
import { SendHorizontalIcon } from "lucide-react";
import { DragAndDropWrapper } from "./drag-drop-wrapper";
import { ComposerAttachments } from "../assistant-ui/attachment";
import { ComposerActionsPopOut } from "./composer-actions-popout";
import { useLanguage } from "@/contexts/LanguageContext";

const getRandomPlaceholder = (searchEnabled: boolean, t: (key: string) => string) => {
  const genericPlaceholders = [
    t("placeholder.shareIdea"),
    t("placeholder.typeVision"),
    t("placeholder.masterpiece"),
    t("placeholder.whatToWrite"),
    t("placeholder.dropIdea"),
    t("placeholder.nextGreat"),
    t("placeholder.shareStory"),
    t("placeholder.writeIncredible"),
    t("placeholder.writingJourney"),
    t("placeholder.contentMagic"),
  ];

  const searchPlaceholders = [
    t("placeholder.shareTopicData"),
    t("placeholder.writeAnything"),
    t("placeholder.ideaResearch"),
    t("placeholder.startFacts"),
    t("placeholder.topicData"),
    t("placeholder.currentInsights"),
    t("placeholder.writeLive"),
    t("placeholder.storyData"),
    t("placeholder.ideasReady"),
    t("placeholder.startFresh"),
  ];

  return searchEnabled
    ? searchPlaceholders[Math.floor(Math.random() * searchPlaceholders.length)]
    : genericPlaceholders[Math.floor(Math.random() * genericPlaceholders.length)];
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};

interface ComposerProps {
  chatStarted: boolean;
  userId: string | undefined;
  searchEnabled: boolean;
}

export const Composer: FC<ComposerProps> = (props: ComposerProps) => {
  const { t } = useLanguage();
  const [placeholder, setPlaceholder] = useState("");

  useEffect(() => {
    setPlaceholder(getRandomPlaceholder(props.searchEnabled, t));
  }, [props.searchEnabled, t]);

  return (
    <DragAndDropWrapper>
      <ComposerPrimitive.Root className="focus-within:border-aui-ring/20 flex flex-col w-full min-h-[64px] flex-wrap items-center justify-center border px-2.5 shadow-sm transition-colors ease-in bg-white rounded-2xl">
        <div className="flex flex-wrap gap-2 items-start mr-auto">
          <ComposerAttachments />
        </div>

        <div className="flex flex-row w-full items-center justify-start my-auto">
          <ComposerActionsPopOut
            userId={props.userId}
            chatStarted={props.chatStarted}
          />
          <ComposerPrimitive.Input
            autoFocus
            placeholder={placeholder}
            rows={1}
            className="placeholder:text-muted-foreground max-h-40 flex-grow resize-none border-none bg-transparent px-2 py-4 text-sm outline-none focus:ring-0 disabled:cursor-not-allowed"
          />
          <ThreadPrimitive.If running={false}>
            <ComposerPrimitive.Send asChild>
              <TooltipIconButton
                tooltip={t("tooltip.send")}
                variant="default"
                className="my-2.5 size-8 p-2 transition-opacity ease-in"
              >
                <SendHorizontalIcon />
              </TooltipIconButton>
            </ComposerPrimitive.Send>
          </ThreadPrimitive.If>
          <ThreadPrimitive.If running>
            <ComposerPrimitive.Cancel asChild>
              <TooltipIconButton
                tooltip={t("common.cancel")}
                variant="default"
                className="my-2.5 size-8 p-2 transition-opacity ease-in"
              >
                <CircleStopIcon />
              </TooltipIconButton>
            </ComposerPrimitive.Cancel>
          </ThreadPrimitive.If>
        </div>
      </ComposerPrimitive.Root>
    </DragAndDropWrapper>
  );
};
