import os
import json
import asyncio
import matplotlib.pyplot as plt
import scienceplots
from mplfonts import use_font
import pandas as pd
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
from pydantic import Field
from typing import Annotated, Any
import openpyxl  # 需要此库来支持pandas读取xlsx文件
from mcp.server.fastmcp import FastMCP

# 初始化 MCP 服务器
mcp = FastMCP("PlottingTool")

# --- 设置中文字体 ---
try:
    use_font('Noto Serif CJK SC')
except Exception as e:
    print(f"警告：设置字体失败，可能影响中文显示。请确保 'Noto Serif CJK SC' 已安装。错误信息: {e}")

# --- 图片保存路径设置 ---
BASE_DIR = r"G:\Study\Vue\agent-chat-ui-main"  # 获取当前脚本所在目录
IMAGES_DIR = os.path.join(BASE_DIR, 'public', 'images')
os.makedirs(IMAGES_DIR, exist_ok=True)


# --- 绘图核心逻辑函数 ---
def _generate_plot_from_excel(excel_path: str, sheet_name: str, filename: str) -> str:
    """
    同步执行的内部函数，负责读取Excel、数据处理和绘图。
    """
    try:
        # 1. 直接从Excel文件读取数据
        # 'engine='openpyxl'' 是为了确保能正确处理.xlsx格式
        df = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')

        # 2. 检查必要的列是否存在
        required_columns = ['日期', '基准水位', '库水位', '入库流量', '出库流量']
        if not all(col in df.columns for col in required_columns):
            missing_cols = [col for col in required_columns if col not in df.columns]
            return json.dumps({"status": "error", "message": f"Excel表单中缺少必要的列：{', '.join(missing_cols)}"})

        # 3. 数据类型转换
        timestamp_col = '日期'
        df[timestamp_col] = pd.to_datetime(df[timestamp_col])

        # 4. 数据提取
        base_level = df['基准水位']
        water_level = df['库水位']
        inner_flow = df['入库流量']
        outer_flow = df['出库流量']

        # 5. 使用 scienceplots 风格进行绘图
        with plt.style.context(['science', 'ieee', 'no-latex', 'cjk-sc-font']):
            # fig, ax1 = plt.subplots(figsize=(8, 4.5))
            fig, ax1 = plt.subplots()
            ax2 = ax1.twinx()

            # 绘制水位
            line1 = ax1.plot(df[timestamp_col], base_level, label='基准水位', linestyle='-', color='C0')
            line2 = ax1.plot(df[timestamp_col], water_level, label='库水位', color=(255 / 255, 33 / 255, 255 / 255),
                             linestyle='-')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('水位 (m)', color='C0')
            ax1.tick_params(axis='y', labelcolor='C0', labelsize=7)
            ax1.yaxis.set_major_locator(MaxNLocator(7))

            # 绘制流量
            line3 = ax2.plot(df[timestamp_col], inner_flow, label='入库流量', linestyle='--',
                             color=(252 / 255, 144 / 255, 107 / 255))
            line4 = ax2.plot(df[timestamp_col], outer_flow, label='出库流量', linestyle='--',
                             color=(88 / 255, 97 / 255, 172 / 255))
            ax2.set_ylabel(r'流量 ($\mathrm{m^3/s}$)')
            ax2.tick_params(axis='y', labelsize=7)
            ax2.yaxis.set_major_locator(MaxNLocator(7))

            # X轴格式化
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y/%m/%d'))
            ax1.tick_params(axis='x', labelsize=7)
            ax1.xaxis.set_major_locator(MaxNLocator(7))
            fig.autofmt_xdate()

            # 合并图例，并放置在图表上方
            lines = line1 + line2 + line3 + line4
            labels = [l.get_label() for l in lines]
            ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, 1.15), ncol=len(labels), fontsize=7)

            # 其他设置
            ax1.minorticks_off()
            ax2.minorticks_off()
            fig.tight_layout()

            # 6. 保存图形
            abs_path = os.path.join(IMAGES_DIR, filename)
            fig.savefig(abs_path, dpi=300)

            # 7. 返回图片的相对路径
            rel_path = os.path.join('images', filename)
            return json.dumps(
                {"status": "success", "message": f"✅ 图片已保存，路径为: {rel_path}", "image_path": rel_path},
                ensure_ascii=False)

    except FileNotFoundError:
        return json.dumps({"status": "error", "message": f"文件未找到：'{excel_path}'"})
    except ValueError as e:
        return json.dumps({"status": "error", "message": f"指定的Excel表单不存在或格式不正确：{e}"})
    except Exception as e:
        return json.dumps({"status": "error", "message": f"绘图失败：{e}"})
    finally:
        plt.close('all')


description = """
该工具用于直接从指定的Excel文件中读取数据并生成包含水位和流量的双Y轴折线图。
它需要Excel文件的完整路径、表单名称，以及一个用于保存图片的文件名。
"""


@mcp.tool()
async def generate_water_level_flow_plot(
        excel_path: Annotated[str, Field(description="Excel文件的完整路径，例如 'C:\\data\\test.xlsx'")],
        sheet_name: Annotated[str, Field(description="Excel表单的名称，例如 'Sheet1'")],
        filename: Annotated[str, Field(description="生成的图片文件名，例如 'water_level_flow.png'")]
) -> str:
    """
    根据指定的Excel文件和表单数据，生成一张双Y轴水位和流量图。

    此工具用于处理水利数据可视化任务，从Excel文件中直接读取数据。
    :param excel_path: Excel文件的完整路径。
    :param sheet_name: Excel文件中包含数据的表单名称。
    :param filename: 指定生成的图片文件名，必须包含文件扩展名（如.png）。
    :return: 包含图片保存状态和相对路径的JSON字符串。
    """
    # 使用 asyncio.to_thread 在单独的线程中运行阻塞的I/O操作（文件读取和绘图）
    result = await asyncio.to_thread(_generate_plot_from_excel, excel_path, sheet_name, filename)
    print("绘图工具执行完毕。")
    return result


if __name__ == "__main__":
    print("绘图 MCP 服务已启动...")
    mcp.run(transport='stdio')